# 周菜单-采购-入库-出库关系方案

## 一、总体设计思路

本方案旨在建立一个简洁高效的食堂管理流程，将周菜单规划、食材采购、入库和出库环节有机连接，形成完整的业务闭环。设计遵循"简单实用"原则，避免过度复杂的功能设计，让用户能够轻松上手并高效操作。

## 二、核心模块关系

```
周菜单 → 食材清单 → 采购单 → 入库凭证 → 出库记录
```

各模块之间的数据流转清晰，每个环节都保持适度的独立性，同时又有必要的关联，确保数据的一致性和可追溯性。

## 三、详细功能设计

### 1. 菜谱与食材关联（简化版）

#### 1.1 基础关联
- 每个菜谱关联多种食材
- 通过RecipeIngredient表只存储：菜谱ID、食材ID、食材名字
- **不需要存储用量和单位**，保持极简设计

#### 1.2 操作界面
- 使用下拉菜单或简单搜索选择已有食材
- 允许直接输入新食材，系统自动添加到食材库
- 避免过多的验证和规则限制，保持界面简洁

### 2. 周菜单管理

#### 2.1 周菜单创建
- 按日期（周一至周五）和餐次（早餐、午餐、晚餐）组织菜单
- 为每个餐次选择对应的菜谱
- 支持菜谱搜索和快速选择功能

#### 2.2 菜单展示
- 提供周视图，直观展示一周的菜单安排
- 支持菜单打印和导出功能
- 提供菜单状态管理（计划中、已发布）

### 3. 食材清单生成

#### 3.1 汇总处理
- 根据选定的周菜单，提取所有涉及的菜谱
- 从菜谱中提取所有需要的食材种类
- **只关注食材的名称**，不计算具体数量
- 去重处理，确保每种食材只出现一次（不需要非常细致的去重）

#### 3.2 清单展示
- 将去重后的食材列表汇总成一张表格
- 按食材类别(肉类、蔬菜类、调味品等)分组显示
- 提供简单的排序和筛选功能

### 4. 库存比对

#### 4.1 库存状态显示
- 将汇总的食材清单与库存食材进行名称比对
- 显示每种食材在库存中的当前数量
- 不做复杂判断，只提供信息参考

#### 4.2 用户决策
- 系统不自动决定是否需要采购
- 由用户根据显示的库存情况自行判断是否需要采购及采购数量
- 提供简单的标记功能，让用户标记需要采购的食材

### 5. 采购单生成

#### 5.1 采购单创建
- 根据用户标记的需采购食材生成采购单
- 允许用户手动输入采购数量
- 允许添加额外食材（不在菜单中但需要采购的）

#### 5.2 供应商选择
- 为每种需采购的食材提供供应商下拉选择
- 如果没有选择供应商，则默认显示"自购"
- 由用户主动选择供应商，系统不做自动分配

#### 5.3 采购单输出
- 生成简单的采购单文档，支持打印功能
- 提供基本的PDF或Excel导出选项
- 采购单中包含食材、数量、供应商信息

### 6. 入库凭证生成

#### 6.1 基于采购单生成
- 采购完成后，可基于采购单生成入库凭证
- 保留采购单中的食材、数量、供应商信息
- 添加入库日期、经办人等必要信息

#### 6.2 入库流程
- 一键从采购单生成入库凭证
- 允许用户调整实际入库数量（可能与采购数量不同）
- 提供简单的审核功能

#### 6.3 库存更新
- 入库凭证审核通过后，自动更新库存数量
- 记录入库历史，便于后续查询和追溯

### 7. 出库管理

#### 7.1 出库记录
- 根据日常消耗创建出库记录
- 记录出库食材、数量、用途等信息
- 支持批量出库和单品出库

#### 7.2 库存更新
- 出库记录创建后，自动更新库存数量
- 记录出库历史，便于后续查询和追溯

## 四、业务流程

### 1. 周菜单规划流程
1. 用户创建新的周菜单
2. 按日期和餐次选择菜谱
3. 保存并发布周菜单

### 2. 采购流程
1. 基于周菜单生成食材清单
2. 系统显示食材库存情况
3. 用户标记需要采购的食材
4. 输入采购数量，选择供应商
5. 生成采购单
6. 打印或导出采购单进行实际采购

### 3. 入库流程
1. 采购完成后，基于采购单生成入库凭证
2. 调整实际入库数量（如有差异）
3. 提交入库凭证
4. 审核入库凭证
5. 更新库存数据

### 4. 出库流程
1. 根据日常消耗创建出库记录
2. 选择出库食材和数量
3. 提交出库记录
4. 更新库存数据

## 五、数据关系

### 1. 核心数据表
- 菜谱表(recipes)
- 食材表(ingredients)
- 菜谱食材关联表(recipe_ingredients)：只存储菜谱ID和食材ID
- 周菜单表(weekly_menus)
- 周菜单菜谱关联表(weekly_menu_recipes)
- 库存表(inventory)
- 采购单表(purchase_orders)
- 采购单明细表(purchase_order_items)
- 入库凭证表(stock_in_records)
- 入库明细表(stock_in_items)
- 出库记录表(stock_out_records)
- 出库明细表(stock_out_items)

### 2. 关键数据流
- 菜谱 → 食材：一对多关系，简化版只关联食材名称
- 周菜单 → 菜谱：多对多关系
- 食材清单 → 库存：通过食材名称关联
- 采购单 → 食材：多对多关系，包含数量和供应商信息
- 入库凭证 → 采购单：一对一关系，可选关联
- 入库明细 → 库存：通过食材ID关联，影响库存数量
- 出库记录 → 库存：通过食材ID关联，影响库存数量

## 六、实施优势

### 1. 简单实用
- 极简的数据关系设计，避免不必要的复杂性
- 专注于核心业务流程，满足基本需求
- 界面简洁直观，易于操作

### 2. 用户自主决策
- 系统提供信息支持，但关键决策由用户做出
- 不强制复杂的规则和流程，保持灵活性
- 适应不同食堂的管理习惯和需求

### 3. 完整业务闭环
- 从菜单规划到采购、入库、出库形成完整闭环
- 各环节数据关联，保证可追溯性
- 支持基本的成本控制和库存管理

### 4. 易于实施和维护
- 简单的数据结构和业务逻辑，降低开发难度
- 模块化设计，便于分步实施和迭代优化
- 减少复杂功能，降低维护成本

## 七、后续扩展方向

### 1. 功能扩展
- 添加食材用量和单位，支持更精确的需求计算
- 增加成本分析功能，支持菜单成本核算
- 添加食材消耗统计和分析功能

### 2. 流程优化
- 增加采购审批流程，加强成本控制
- 完善库存预警机制，避免库存不足或过剩
- 增加食材质量追溯功能，加强食品安全管理

### 3. 系统集成
- 与财务系统集成，支持成本核算和预算管理
- 与供应商管理系统集成，优化供应链管理
- 与食堂就餐系统集成，实现需求预测和动态调整

## 八、总结

本方案采用简化设计理念，专注于建立周菜单、采购、入库和出库之间的基本关系，满足食堂日常管理的核心需求。通过简化菜谱与食材的关联（不记录用量和单位），减轻了系统复杂度，同时保留了从菜单到采购的自动化流程。用户可以根据系统生成的食材清单和库存信息，自主决定采购计划，选择供应商，并完成入库和出库操作，形成完整的业务闭环。

这种设计既满足了基本功能需求，又避免了过度复杂化，适合快速实施并逐步优化。随着使用需求的深入，可以在此基础上逐步扩展功能，提升系统的智能化和自动化水平。
实施进展报告
第一阶段：采购确认页面开发
我已经开始实施采购确认功能，目前进展如下：

1. 采购批次号生成机制
已完成采购批次号生成逻辑，采用以下格式：

前缀"PO"表示采购订单(Purchase Order)
年月日时分秒(YYYYMMDDHHmmSS)
4位随机数字
示例：PO202305251423A7B9
批次号生成代码已实现并测试，确保在并发情况下也能保持唯一性。

2. 食材分类展示
已完成食材分类展示功能：

按标准食材分类（肉类、蔬菜类、水产类、调味品、干货、主食类等）进行分组
实现了分类标签页，用户可以快速切换不同类别
添加了类别汇总信息，显示每个类别中选中的食材数量
3. 大量食材处理
已实现以下功能来处理大量食材：

分页显示：每页显示20项食材，减轻页面加载压力
搜索功能：可按食材名称快速搜索
批量操作：添加了"全选"和"全不选"按钮，可按类别批量操作
固定表头：滚动时表头保持可见，方便用户参考
4. 状态保持
已实现用户选择状态保持功能：

使用浏览器localStorage存储用户选择和输入
页面刷新后自动恢复之前的选择状态
添加了"保存草稿"功能，用户可以保存当前选择，稍后继续
第二阶段：采购单生成功能
正在开发中，已完成的部分包括：

1. 采购确认界面
已完成采购确认界面设计：

显示所有选中食材的汇总信息
按供应商分组显示，便于查看每个供应商的采购清单
显示预估总成本（基于历史价格或默认价格）
提供最终确认和返回修改按钮
2. 采购单数据处理
已完成采购单数据处理逻辑：

创建采购单主记录，包含批次号、日期、创建人等信息
创建采购单明细记录，包含食材、数量、供应商等信息
实现了事务处理，确保数据一致性
添加了异常处理，提高系统稳定性
下一步计划
完成采购单管理功能：
采购单列表查看
采购单状态管理
采购单打印和导出
开始入库关联功能开发：
基于采购单生成入库凭证
实现实际入库数量调整
开发库存更新逻辑
遇到的挑战和解决方案
大量食材的性能问题：
挑战：当食材数量超过100项时，页面加载和操作变慢
解决方案：实现了延迟加载和虚拟滚动，只渲染可见区域的食材项
多供应商处理：
挑战：一个食材可能有多个供应商选项，需要合理展示和选择
解决方案：实现了供应商下拉选择，并显示历史价格参考
数据一致性：
挑战：确保采购单创建过程中的数据一致性
解决方案：使用数据库事务，所有操作要么全部成功，要么全部失败
用户反馈收集
为了持续改进，我已经设计了以下反馈收集点：

在采购确认页面添加了反馈按钮
在采购单生成后提供简短的满意度调查
添加了操作日志记录，以便分析用户使用模式