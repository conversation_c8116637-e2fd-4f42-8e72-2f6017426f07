# 入库模块数据库表结构

本文档描述了入库模块涉及的数据库表及其结构。

## 1. 仓库表 (warehouses)

仓库表用于存储食堂的仓库信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| name | String(100) | 否 | - | 仓库名称 |
| area_id | Integer | 否 | - | 所属区域ID，外键关联administrative_areas表 |
| location | String(200) | 否 | - | 位置 |
| manager_id | Integer | 否 | - | 管理员ID，外键关联users表 |
| capacity | Float | 是 | - | 容量 |
| capacity_unit | String(20) | 是 | - | 容量单位 |
| temperature_range | String(50) | 是 | - | 温度范围 |
| humidity_range | String(50) | 是 | - | 湿度范围 |
| status | String(20) | 否 | '正常' | 状态，可选值：正常/维护中/已关闭 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |
| updated_at | DATETIME2(1) | 否 | 当前时间 | 更新时间 |

## 2. 存储位置表 (storage_locations)

存储位置表用于存储仓库内的具体存储位置信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| warehouse_id | Integer | 否 | - | 所属仓库ID，外键关联warehouses表 |
| name | String(100) | 否 | - | 存储位置名称 |
| location_code | String(50) | 否 | - | 位置编码 |
| storage_type | String(50) | 否 | - | 存储类型，如：冷藏/冷冻/常温 |
| capacity | Float | 是 | - | 容量 |
| capacity_unit | String(20) | 是 | - | 容量单位 |
| temperature_range | String(50) | 是 | - | 温度范围 |
| status | String(20) | 否 | '正常' | 状态，可选值：正常/维护中/已关闭 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |
| updated_at | DATETIME2(1) | 否 | 当前时间 | 更新时间 |

## 3. 入库单表 (stock_ins)

入库单表用于记录食材入库信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| stock_in_number | String(50) | 否 | - | 入库单号，唯一 |
| warehouse_id | Integer | 否 | - | 仓库ID，外键关联warehouses表 |
| delivery_id | Integer | 是 | - | 送货单ID，外键关联supplier_deliveries表 |
| purchase_order_id | Integer | 是 | - | 采购订单ID，外键关联purchase_orders表 |
| stock_in_date | DATETIME2(1) | 否 | - | 入库日期 |
| stock_in_type | String(20) | 否 | - | 入库类型，如：采购入库/调拨入库/退货入库 |
| operator_id | Integer | 否 | - | 操作员ID，外键关联users表 |
| inspector_id | Integer | 是 | - | 检查员ID，外键关联users表 |
| status | String(20) | 否 | '待审核' | 状态，可选值：待审核/已审核/已入库/已取消 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |
| updated_at | DATETIME2(1) | 否 | 当前时间 | 更新时间 |

## 4. 入库明细表 (stock_in_items)

入库明细表用于记录入库单中的具体食材明细。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| stock_in_id | Integer | 否 | - | 入库单ID，外键关联stock_ins表 |
| ingredient_id | Integer | 否 | - | 食材ID，外键关联ingredients表 |
| purchase_order_item_id | Integer | 是 | - | 采购订单明细ID，外键关联purchase_order_items表 |
| storage_location_id | Integer | 否 | - | 存储位置ID，外键关联storage_locations表 |
| batch_number | String(50) | 否 | - | 批次号 |
| quantity | Float | 否 | - | 数量 |
| unit | String(20) | 否 | - | 单位 |
| production_date | Date | 否 | - | 生产日期 |
| expiry_date | Date | 否 | - | 过期日期 |
| unit_price | Float | 是 | - | 单价 |
| supplier_id | Integer | 是 | - | 供应商ID，外键关联suppliers表 |
| quality_status | String(20) | 是 | '良好' | 质量状态 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |
| updated_at | DATETIME2(1) | 否 | 当前时间 | 更新时间 |

## 5. 入库单据表 (stock_in_documents)

入库单据表用于存储与入库相关的文档信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| stock_in_id | Integer | 否 | - | 入库单ID，外键关联stock_ins表 |
| document_type | String(50) | 否 | - | 文档类型，如：送货单/检验检疫证明/质量检测报告 |
| file_path | String(255) | 否 | - | 文件路径 |
| supplier_id | Integer | 是 | - | 供应商ID，外键关联suppliers表 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |

## 6. 入库单据与入库明细关联表 (stock_in_document_items)

入库单据与入库明细的多对多关联表。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| document_id | Integer | 否 | - | 文档ID，外键关联stock_in_documents表，主键之一 |
| item_id | Integer | 否 | - | 入库明细ID，外键关联stock_in_items表，主键之一 |

## 7. 食材检验检疫记录表 (ingredient_inspections)

食材检验检疫记录表用于记录入库食材的检验检疫信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| stock_in_item_id | Integer | 否 | - | 入库明细ID，外键关联stock_in_items表 |
| inspector_id | Integer | 否 | - | 检查员ID，外键关联users表 |
| inspection_date | Date | 否 | 当前日期 | 检验日期 |
| inspection_type | String(50) | 否 | - | 检验类型，如：感官检验/理化检验/微生物检验 |
| result | String(20) | 否 | - | 检验结果，如：合格/不合格 |
| notes | Text | 是 | - | 备注 |
| document_id | Integer | 是 | - | 文档ID，外键关联stock_in_documents表 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |

## 8. 库存表 (inventories)

库存表用于记录当前库存信息。

| 字段名 | 数据类型 | 是否为空 | 默认值 | 说明 |
|-------|---------|---------|-------|------|
| id | Integer | 否 | 自增 | 主键 |
| warehouse_id | Integer | 否 | - | 仓库ID，外键关联warehouses表 |
| storage_location_id | Integer | 否 | - | 存储位置ID，外键关联storage_locations表 |
| ingredient_id | Integer | 否 | - | 食材ID，外键关联ingredients表 |
| batch_number | String(50) | 否 | - | 批次号 |
| quantity | Float | 否 | - | 数量 |
| unit | String(20) | 否 | - | 单位 |
| production_date | Date | 否 | - | 生产日期 |
| expiry_date | Date | 否 | - | 过期日期 |
| supplier_id | Integer | 是 | - | 供应商ID，外键关联suppliers表 |
| status | String(20) | 否 | '正常' | 状态，可选值：正常/待检/冻结/已过期 |
| notes | Text | 是 | - | 备注 |
| created_at | DATETIME2(1) | 否 | 当前时间 | 创建时间 |
| updated_at | DATETIME2(1) | 否 | 当前时间 | 更新时间 |


是的，这些表已经在您的数据库中建好了。"入库表.MD"文件是我刚刚根据您项目中的数据库模型定义和迁移文件创建的，它只是一个文档，用于记录和说明入库模块相关的数据库表结构。

这个文档中描述的所有表（warehouses、storage_locations、stock_ins、stock_in_items、stock_in_documents、stock_in_document_items、ingredient_inspections、inventories）都已经在您的SQL Server数据库中创建完成，并且正在被您的应用程序使用。

从我们之前修复的"从采购计划导入"功能可以看出，这些表已经在正常工作，只是在JavaScript代码中有一些小问题导致功能不正常，我们已经修复了这些问题。

这个MD文档只是为了方便您查阅和理解入库模块的数据库结构，不需要再次创建这些表。