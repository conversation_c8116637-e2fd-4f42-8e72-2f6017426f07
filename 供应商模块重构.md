# 供应商模块重构

## 问题分析

在供应商模块中，我们发现了以下问题：

1. **数据库精度问题**：在创建供应商和供应商-学校关系时，出现了 DATETIME2 字段的精度问题，导致以下错误：
   ```
   pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
   ```

2. **审计日志创建方式不一致**：在 supplier.py 中，直接创建了 AuditLog 对象并添加到会话中，而在其他地方使用了 log_activity 函数来记录审计日志。

## 解决方案

### 1. 修复数据库精度问题

我们创建了一个数据库迁移脚本 `fix_datetime_precision.py`，用于修复 DATETIME2 字段的精度问题。该脚本将所有 DATETIME2 字段的精度从 0 修改为 1，以解决精度问题。

### 2. 统一审计日志创建方式

我们修改了 supplier.py 中的代码，使用 log_activity 函数来记录审计日志，而不是直接创建 AuditLog 对象。这样可以确保所有审计日志的创建方式一致，避免精度问题。

### 3. 优化导入

我们优化了 supplier.py 中的导入，删除了不必要的导入，并将 log_activity 函数的导入移到文件顶部，避免重复导入。

## 具体修改

### 1. 修改 supplier.py

- 将直接创建 AuditLog 对象的代码替换为使用 log_activity 函数
- 优化导入，删除不必要的导入，将 log_activity 函数的导入移到文件顶部

### 2. 创建数据库迁移脚本

创建了 `fix_datetime_precision.py` 脚本，用于修复以下表的 DATETIME2 字段的精度问题：

- supplier_school_relations
- audit_logs
- suppliers
- product_spec_parameters
- delivery_inspections
- delivery_item_inspections
- supplier_products

## 后续工作

1. **执行数据库迁移**：需要执行数据库迁移脚本，修复 DATETIME2 字段的精度问题。

   ```bash
   flask db upgrade
   ```

2. **测试供应商模块**：测试供应商模块的各项功能，确保修改后的代码能够正常工作。

3. **监控系统日志**：监控系统日志，确保不再出现精度问题的错误。

## 总结

通过这次重构，我们解决了供应商模块中的数据库精度问题和审计日志创建方式不一致的问题，提高了系统的稳定性和可维护性。同时，我们也优化了代码结构，使其更加清晰和一致。

## 注意事项

1. 在使用 SQLAlchemy 的 DATETIME2 类型时，必须指定精度，例如 DATETIME2(precision=1)。
2. 在使用 datetime.now() 时，应该使用 lambda 函数来替换微秒，避免精度问题。
3. 在记录审计日志时，应该使用 log_activity 函数，而不是直接创建 AuditLog 对象。
