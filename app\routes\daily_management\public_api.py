"""
公开访问的API路由

提供公开访问的API接口，用于照片上传和评分。
"""

from flask import request, jsonify, current_app
from app.routes.daily_management import daily_management_bp
from app import db
from app.models_daily_management import DailyLog, InspectionRecord, Photo
from app.models import AdministrativeArea
from sqlalchemy import text
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from PIL import Image
import io
import traceback

# 公开上传检查照片API
@daily_management_bp.route('/api/v2/inspections/public/upload-photos/<int:school_id>/<int:log_id>/<inspection_type>', methods=['POST'])
def public_upload_inspection_photos(school_id, log_id, inspection_type):
    """公开上传检查照片API"""
    try:
        # 验证学校和日志
        school = AdministrativeArea.query.get(school_id)
        if not school:
            return jsonify({'success': False, 'error': '学校不存在'}), 404

        log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first()
        if not log:
            return jsonify({'success': False, 'error': '日志不存在'}), 404

        # 验证检查类型
        if inspection_type not in ['morning', 'noon', 'evening']:
            return jsonify({'success': False, 'error': '检查类型无效'}), 400

        # 获取检查项目（简化为时段检查）
        inspection_item = request.form.get('inspection_item', f'{inspection_type}_inspection')

        # 获取描述
        description = request.form.get('description', '')

        # 获取照片
        if 'photos' not in request.files:
            return jsonify({'success': False, 'error': '没有上传照片'}), 400

        photos = request.files.getlist('photos')
        if not photos or not photos[0].filename:
            return jsonify({'success': False, 'error': '没有选择照片'}), 400

        # 查找或创建检查记录（按时段统一管理）
        inspection = InspectionRecord.query.filter_by(
            daily_log_id=log_id,
            inspection_type=inspection_type,
            inspection_item=inspection_item
        ).first()

        if not inspection:
            # 创建新的检查记录
            inspection_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 使用原始SQL创建检查记录
            sql = text("""
                INSERT INTO inspection_records
                (daily_log_id, inspection_type, inspection_item, status, description, inspection_time)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :inspection_type, :inspection_item, :status, :description,
                 CONVERT(DATETIME2(1), :inspection_time, 120))
            """)

            result = db.session.execute(sql, {
                'daily_log_id': log_id,
                'inspection_type': inspection_type,
                'inspection_item': inspection_item,
                'status': 'normal',
                'description': description,
                'inspection_time': inspection_time_str
            })

            inspection_id = result.fetchone()[0]
            db.session.commit()

            # 创建一个临时对象用于后续处理
            class TempInspection:
                def __init__(self, id):
                    self.id = id

            inspection = TempInspection(inspection_id)

        # 上传照片 - 直接使用时段作为reference_type，日志ID作为reference_id
        uploaded_photos = []
        for photo_file in photos:
            if photo_file and photo_file.filename:
                try:
                    # 处理照片 - 使用时段类型和日志ID
                    photo_result = handle_photo_upload(photo_file, inspection_type, log_id, description)
                    if photo_result:
                        uploaded_photos.append(photo_result)
                except Exception as e:
                    current_app.logger.error(f"处理照片失败: {str(e)}")
                    current_app.logger.error(traceback.format_exc())

        if not uploaded_photos:
            return jsonify({'success': False, 'error': '照片上传失败'}), 500

        return jsonify({
            'success': True,
            'message': f'成功上传 {len(uploaded_photos)} 张照片',
            'photos': uploaded_photos
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"公开上传检查照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500

# 公开评分照片API
@daily_management_bp.route('/api/v2/photos/public/rate', methods=['POST'])
def public_rate_photo():
    """公开评分照片API"""
    try:
        data = request.json
        if not data or 'photo_id' not in data or 'rating' not in data:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        photo_id = data['photo_id']
        rating = data['rating']

        # 验证评分范围
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return jsonify({'success': False, 'error': '评分必须是1-5之间的整数'}), 400

        # 更新照片评分
        sql = text("""
        UPDATE photos
        SET rating = :rating
        WHERE id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id, 'rating': rating})
        if result.rowcount == 0:
            return jsonify({'success': False, 'error': '照片不存在'}), 404

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '评分成功',
            'photo_id': photo_id,
            'rating': rating
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"公开评分照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': f'评分失败: {str(e)}'}), 500

def handle_photo_upload(photo_file, reference_type, reference_id, description=''):
    """处理照片上传，返回照片路径"""
    if not photo_file or not photo_file.filename:
        return None

    try:
        filename = secure_filename(photo_file.filename)
        # 生成唯一文件名
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

        # 确保目录存在
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            reference_type
        )
        os.makedirs(upload_folder, exist_ok=True)

        # 处理图片 - 压缩处理
        img = Image.open(photo_file)

        # 转换为RGB模式（如果是RGBA或其他模式）
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')

        # 调整大小，保持宽高比
        max_size = (800, 600)
        img.thumbnail(max_size, Image.Resampling.LANCZOS)

        # 压缩质量
        output = io.BytesIO()
        # 统一保存为JPEG格式
        img.save(output, format='JPEG', quality=85, optimize=True)
        output.seek(0)

        # 更新文件名为.jpg
        if not unique_filename.lower().endswith('.jpg'):
            unique_filename = os.path.splitext(unique_filename)[0] + '.jpg'

        # 保存处理后的图片
        file_path = os.path.join(upload_folder, unique_filename)
        with open(file_path, 'wb') as f:
            f.write(output.getvalue())

        # 相对路径（用于数据库存储）
        relative_path = f"/static/uploads/daily_management/{reference_type}/{unique_filename}"

        # 创建照片记录 - 使用时段作为reference_type
        sql = text("""
        INSERT INTO photos
        (reference_id, reference_type, file_name, file_path, description, rating, upload_time)
        OUTPUT inserted.id, inserted.file_path
        VALUES
        (:reference_id, :reference_type, :file_name, :file_path, :description, :rating,
         CONVERT(DATETIME2(1), :upload_time, 120))
        """)

        result = db.session.execute(sql, {
            'reference_id': reference_id,
            'reference_type': reference_type,  # 这里会是 'morning', 'noon', 'evening'
            'file_name': unique_filename,
            'file_path': relative_path,
            'description': description,
            'rating': 3,  # 默认评分为3星
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        row = result.fetchone()
        db.session.commit()

        return {
            'id': row[0],
            'file_path': row[1]
        }

    except Exception as e:
        current_app.logger.error(f"处理照片上传失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        raise
