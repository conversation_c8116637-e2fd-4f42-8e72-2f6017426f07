{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap4.min.css') }}">
<style>
    .action-buttons .btn {
        margin-right: 5px;
    }

    .form-control {
        font-size: 1rem;
    }

    label {
        font-size: 1.05rem;
        font-weight: 500;
    }

    .form-section {
        background-color: #f8f9fc;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #4e73df;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .section-title {
        font-weight: 600;
        color: #4e73df;
        margin-bottom: 20px;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 10px;
        font-size: 1.2rem;
    }

    .menu-display {
        background-color: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #2196f3;
    }

    .menu-title {
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 10px;
        font-size: 1.1rem;
    }

    .menu-items {
        color: #424242;
        line-height: 1.6;
    }

    .menu-item {
        display: inline-block;
        background-color: #fff;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        font-size: 0.9rem;
    }

    .meal-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .meal-breakfast {
        background-color: #4e73df;
        color: white;
    }

    .meal-lunch {
        background-color: #1cc88a;
        color: white;
    }

    .meal-dinner {
        background-color: #f6c23e;
        color: white;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .btn-primary:hover {
        background-color: #2e59d9;
        border-color: #2653d4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'daily_log') }}

    <!-- 功能按钮 -->
    <div class="mb-4">
        {% if log %}
        <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-info">
            <i class="fas fa-clipboard-check mr-1"></i> 检查记录
        </a>
        <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-success">
            <i class="fas fa-user-friends mr-1"></i> 陪餐记录
        </a>
        <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-warning">
            <i class="fas fa-graduation-cap mr-1"></i> 培训记录
        </a>
        <a href="{{ url_for('daily_management.events', log_id=log.id) }}" class="btn btn-secondary">
            <i class="fas fa-calendar-alt mr-1"></i> 特殊事件
        </a>
        <a href="{{ url_for('daily_management.issues', log_id=log.id) }}" class="btn btn-danger">
            <i class="fas fa-exclamation-triangle mr-1"></i> 问题记录
        </a>
        <a href="{{ url_for('daily_management.print_log', log_id=log.id) }}" class="btn btn-primary">
            <i class="fas fa-print mr-1"></i> 打印日志
        </a>
        {% endif %}
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left mr-1"></i> 返回首页
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-edit mr-1"></i> {% if log %}编辑{% else %}创建{% endif %}日志信息
            </h6>
            <div>
                <span class="badge badge-info">{{ log_date.strftime('%Y年%m月%d日') }}</span>
                <span class="badge badge-secondary ml-1">
                    {% set weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] %}
                    {{ weekdays[log_date.weekday()] }}
                </span>
            </div>
        </div>
        <div class="card-body">
            <form method="post" id="logForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <!-- 基本信息部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-info-circle"></i>
                        基本信息
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="weather">天气</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-cloud-sun"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="weather" name="weather"
                                           value="{{ log.weather if log else '' }}" placeholder="请输入当日天气">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="manager">管理员</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="manager" name="manager"
                                           value="{{ log.manager if log else '' }}" placeholder="请输入管理员姓名">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 就餐人数部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-users"></i>
                        就餐人数统计
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="student_count">学生就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user-graduate"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="student_count" name="student_count"
                                           value="{{ log.student_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="teacher_count">教师就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-chalkboard-teacher"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="teacher_count" name="teacher_count"
                                           value="{{ log.teacher_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="other_count">其他就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="other_count" name="other_count"
                                           value="{{ log.other_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-1"></i> 总就餐人数:
                                <span id="total-count" class="font-weight-bold">
                                    {{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}
                                </span> 人
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 菜单信息部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-utensils"></i>
                        菜单信息
                    </div>

                    <!-- 显示当天菜谱信息 -->
                    {% if menu_data %}
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="menu-display">
                                <div class="menu-title">
                                    <span class="meal-badge meal-breakfast">早餐</span>
                                    当日菜谱
                                </div>
                                <div class="menu-items">
                                    {% if menu_data.breakfast %}
                                        {% for item in menu_data.breakfast %}
                                        <span class="menu-item">{{ item }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">暂无早餐菜谱</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="menu-display">
                                <div class="menu-title">
                                    <span class="meal-badge meal-lunch">午餐</span>
                                    当日菜谱
                                </div>
                                <div class="menu-items">
                                    {% if menu_data.lunch %}
                                        {% for item in menu_data.lunch %}
                                        <span class="menu-item">{{ item }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">暂无午餐菜谱</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="menu-display">
                                <div class="menu-title">
                                    <span class="meal-badge meal-dinner">晚餐</span>
                                    当日菜谱
                                </div>
                                <div class="menu-items">
                                    {% if menu_data.dinner %}
                                        {% for item in menu_data.dinner %}
                                        <span class="menu-item">{{ item }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">暂无晚餐菜谱</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 菜单输入框 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="breakfast_menu">早餐菜单</label>
                                <textarea class="form-control" id="breakfast_menu" name="breakfast_menu"
                                          rows="4" placeholder="请输入早餐菜单...">{{ log.breakfast_menu if log else (', '.join(menu_data.breakfast) if menu_data.breakfast else '') }}</textarea>
                                <small class="form-text text-muted">
                                    {% if menu_data.breakfast %}
                                    <i class="fas fa-lightbulb text-warning"></i> 已自动填入当日菜谱，可根据实际情况调整
                                    {% else %}
                                    <i class="fas fa-info-circle text-info"></i> 请输入实际供应的早餐菜单
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="lunch_menu">午餐菜单</label>
                                <textarea class="form-control" id="lunch_menu" name="lunch_menu"
                                          rows="4" placeholder="请输入午餐菜单...">{{ log.lunch_menu if log else (', '.join(menu_data.lunch) if menu_data.lunch else '') }}</textarea>
                                <small class="form-text text-muted">
                                    {% if menu_data.lunch %}
                                    <i class="fas fa-lightbulb text-warning"></i> 已自动填入当日菜谱，可根据实际情况调整
                                    {% else %}
                                    <i class="fas fa-info-circle text-info"></i> 请输入实际供应的午餐菜单
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="dinner_menu">晚餐菜单</label>
                                <textarea class="form-control" id="dinner_menu" name="dinner_menu"
                                          rows="4" placeholder="请输入晚餐菜单...">{{ log.dinner_menu if log else (', '.join(menu_data.dinner) if menu_data.dinner else '') }}</textarea>
                                <small class="form-text text-muted">
                                    {% if menu_data.dinner %}
                                    <i class="fas fa-lightbulb text-warning"></i> 已自动填入当日菜谱，可根据实际情况调整
                                    {% else %}
                                    <i class="fas fa-info-circle text-info"></i> 请输入实际供应的晚餐菜单
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他信息部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-clipboard-list"></i>
                        其他信息
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="food_waste">食物浪费量(kg)</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-trash"></i></span>
                                    </div>
                                    <input type="number" step="0.01" class="form-control" id="food_waste" name="food_waste"
                                           value="{{ log.food_waste if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="special_events">特殊事件概述</label>
                                <textarea class="form-control" id="special_events" name="special_events"
                                          rows="3" placeholder="请简要描述当日特殊事件...">{{ log.special_events if log else '' }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="operation_summary">运营总结</label>
                        <textarea class="form-control" id="operation_summary" name="operation_summary"
                                  rows="4" placeholder="请输入当日运营总结...">{{ log.operation_summary if log else '' }}</textarea>
                    </div>
                </div>

                <div class="form-group text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save mr-1"></i> 保存日志
                    </button>
                    <a href="{{ url_for('daily_management.logs') }}" class="btn btn-secondary btn-lg ml-2">
                        <i class="fas fa-arrow-left mr-1"></i> 返回列表
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ url_for('static', filename='vendor/datatables/js/dataTables.bootstrap4.min.js') }}"></script>
<script>
    $(document).ready(function() {
        // 计算总就餐人数
        function calculateTotal() {
            var studentCount = parseInt($('#student_count').val()) || 0;
            var teacherCount = parseInt($('#teacher_count').val()) || 0;
            var otherCount = parseInt($('#other_count').val()) || 0;
            var total = studentCount + teacherCount + otherCount;
            $('#total-count').text(total);
        }

        // 监听就餐人数输入框的变化
        $('#student_count, #teacher_count, #other_count').on('input', calculateTotal);

        // 菜谱自动填充功能
        {% if menu_data %}
        // 如果菜单输入框为空且有菜谱数据，则自动填充
        {% if menu_data.breakfast and not (log and log.breakfast_menu) %}
        if (!$('#breakfast_menu').val().trim()) {
            $('#breakfast_menu').val('{{ ", ".join(menu_data.breakfast) }}');
        }
        {% endif %}

        {% if menu_data.lunch and not (log and log.lunch_menu) %}
        if (!$('#lunch_menu').val().trim()) {
            $('#lunch_menu').val('{{ ", ".join(menu_data.lunch) }}');
        }
        {% endif %}

        {% if menu_data.dinner and not (log and log.dinner_menu) %}
        if (!$('#dinner_menu').val().trim()) {
            $('#dinner_menu').val('{{ ", ".join(menu_data.dinner) }}');
        }
        {% endif %}
        {% endif %}

        // 添加菜谱项目到输入框的功能
        $('.menu-item').on('click', function() {
            var menuText = $(this).text();
            var mealType = '';

            // 确定餐次类型
            if ($(this).closest('.col-md-4').index() === 0) {
                mealType = 'breakfast';
            } else if ($(this).closest('.col-md-4').index() === 1) {
                mealType = 'lunch';
            } else {
                mealType = 'dinner';
            }

            var textarea = $('#' + mealType + '_menu');
            var currentValue = textarea.val();

            // 如果当前值为空，直接设置
            if (!currentValue.trim()) {
                textarea.val(menuText);
            } else {
                // 如果已有内容，检查是否已包含该项目
                if (currentValue.indexOf(menuText) === -1) {
                    textarea.val(currentValue + ', ' + menuText);
                }
            }

            // 调整文本区域高度
            textarea.trigger('input');

            // 高亮显示点击的项目
            $(this).addClass('bg-primary text-white').delay(300).queue(function() {
                $(this).removeClass('bg-primary text-white').dequeue();
            });
        });

        // 表单验证
        $('#logForm').on('submit', function(e) {
            // 显示加载状态
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        });

        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();

        // 自动调整文本区域高度
        $('textarea').each(function() {
            this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
        }).on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // 菜谱项目悬停效果
        $('.menu-item').hover(
            function() {
                $(this).addClass('shadow-sm').css('cursor', 'pointer');
            },
            function() {
                $(this).removeClass('shadow-sm');
            }
        );

        // 添加菜谱提示
        $('.menu-item').attr('title', '点击添加到菜单输入框');
        $('[title]').tooltip();
    });
</script>
{% endblock %}
